package com.bzlj.craft.transform.service;

import com.bzlj.base.search.SearchCondition;
import com.bzlj.base.search.SearchItem;
import com.bzlj.base.search.SearchItems;
import com.bzlj.craft.entity.*;
import com.bzlj.craft.repository.ParameterDefinitionExtendRepository;
import com.bzlj.craft.repository.ParameterDefinitionRepository;
import com.bzlj.craft.repository.StepParameterRepository;
import com.bzlj.craft.repository.WorkStepRepository;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.transform.cache.ParamCacheManagerService;
import com.bzlj.craft.transform.enums.ParamConfirmed;
import com.bzlj.craft.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2025-06-06 15:04
 */
@Service
@Slf4j
public class CraftParamTransformService {

    @Autowired
    private ParamCacheManagerService cacheManager;

    @Autowired
    private ISurveillanceService surveillanceService;

    @Autowired
    private WorkStepRepository workStepRepository;

    @Autowired
    private StepParameterRepository stepParameterRepository;

    @Autowired
    private ParameterDefinitionRepository parameterDefinitionRepository;

    @Autowired
    private ParameterDefinitionExtendRepository parameterDefinitionExtendRepository;
    public void craftParamsTransform(String json) {
        if (StringUtils.isBlank(json)) {
            throw new RuntimeException("输入参数不能为空");
        }
        try {
            // 解析JSON
            JsonNode jsonNode = JsonUtils.toJsonNode(json);
            if(jsonNode instanceof ArrayNode){
                jsonNode.forEach(node -> {
                    craftParamsTransform(node.toString());
                });
                return;
            }
            processParamNode(jsonNode);

        } catch (Exception e) {
            log.error("工艺参数转换失败: {}", e.getMessage(), e);
            throw new RuntimeException("工艺参数转换失败: " + e.getMessage());
        }
    }

    /**
     * 处理单个参数节点
     * @param paramNode 参数节点
     */
    private void processParamNode(JsonNode paramNode) {
        // 验证必要字段
        String taskCode = getRequiredField(paramNode, "taskCode");
        String paramCode = getRequiredField(paramNode, "paramCode");
        String processCode = getRequiredField(paramNode, "processCode");

        log.info("处理工艺参数 - taskCode: {}, processCode: {}, paramCode: {}", taskCode, processCode, paramCode);

        // 1. 根据taskCode和processCode查询ProductionTask
        ProductionTask productionTask = findProductionTask(taskCode, processCode);
        if (productionTask == null) {
            throw new RuntimeException(String.format("未找到对应的生产任务 - taskCode: %s, processCode: %s", taskCode, processCode));
        }

        // 2. 根据stepId和taskId查询WorkStep
        WorkStep workStep = workStepRepository.findFirstByStepIdAndTaskTaskIdAndDeleted(
                productionTask.getStep().getId(),
                productionTask.getTaskId(),
                false
        );
        if (workStep == null) {
            throw new RuntimeException(String.format("未找到对应的执行工步 - stepId: %s, taskId: %s",
                    productionTask.getStep().getId(), productionTask.getTaskId()));
        }

        // 3. 根据workStep查询ParameterDefinition
        List<ParameterDefinition> parameterDefinitions = parameterDefinitionRepository.findByWorkStepWorkStepId(workStep.getWorkStepId());
        Map<String, ParameterDefinition> paramDefMap = Maps.uniqueIndex(parameterDefinitions, ParameterDefinition::getParamCodee);

        // 4. 解析paramCode，获取参数名称列表
        String[] paramNames = paramCode.split(",");

        // 5. 更新参数值
        for (String paramName : paramNames) {
            paramName = paramName.trim();
            if (StringUtils.isNotBlank(paramName)) {
                updateParameterValue(paramNode, paramDefMap, paramName);
            }
        }
    }

    /**
     * 获取必要字段值
     * @param node JSON节点
     * @param fieldName 字段名
     * @return 字段值
     */
    private String getRequiredField(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode == null || fieldNode.isNull() || StringUtils.isBlank(fieldNode.asText())) {
            throw new RuntimeException(String.format("缺少必要字段: %s", fieldName));
        }
        return fieldNode.asText();
    }

    /**
     * 根据taskCode和processCode查询ProductionTask
     * @param taskCode 任务编码
     * @param processCode 工序编码
     * @return ProductionTask对象
     */
    private ProductionTask findProductionTask(String taskCode, String processCode) {
        SearchCondition condition = new SearchCondition();
        condition.setOpenProps(Lists.newArrayList("step", "process"));
        condition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("taskCode", taskCode, null, SearchItem.Operator.EQ))
                .item(new SearchItem("process.processCode", processCode, null, SearchItem.Operator.EQ))
                .item(new SearchItem("deleted", false, null, SearchItem.Operator.EQ))
                .build());

        return surveillanceService.findOne(condition);
    }

    /**
     * 更新参数定义的最小值和最大值
     * @param paramNode 参数节点
     * @param paramDefMap 参数定义映射
     * @param paramName 参数名称
     */
    private void updateParameterValue(JsonNode paramNode, Map<String, ParameterDefinition> paramDefMap, String paramName) {
        // 获取参数对象
        JsonNode paramValueNode = paramNode.get(paramName);
        if (paramValueNode == null || paramValueNode.isNull()) {
            log.warn("参数节点中未找到参数: {}", paramName);
            return;
        }

        // 获取MIN和MAX值
        JsonNode minNode = paramValueNode.get("MIN");
        JsonNode maxNode = paramValueNode.get("MAX");

        if (minNode == null || maxNode == null) {
            log.warn("参数 {} 缺少MIN或MAX值", paramName);
//            return;
        }

        // 查找对应的参数定义
        ParameterDefinition paramDef = paramDefMap.get(paramName);
        if (paramDef == null) {
            log.warn("未找到参数定义: {}", paramName);
            return;
        }

        try {
            if(Objects.nonNull(minNode) && StringUtils.isNotBlank(minNode.asText())){
                paramDef.setMinValue(new BigDecimal(minNode.asText()));
            }

            if(Objects.nonNull(maxNode) && StringUtils.isNotBlank(maxNode.asText())){
                paramDef.setMaxValue(new BigDecimal(maxNode.asText()));
            }

            // 保存更新
            parameterDefinitionRepository.save(paramDef);

            log.info("成功更新参数 {} 的值范围: MIN={}, MAX={}", paramName, paramDef.getMinValue(), paramDef.getMaxValue());

        } catch (NumberFormatException e) {
            log.error("参数 {} 的MIN或MAX值格式错误: MIN={}, MAX={}", paramName, minNode.asText(), maxNode.asText());
            throw new RuntimeException(String.format("参数 %s 的数值格式错误", paramName));
        }
    }

    public void generateParams(String pono, ArrayNode craftParams) {
        SearchCondition condition = new SearchCondition();
        condition.setOpenProps(Lists.newArrayList("step"));
        condition.setSearchItems(SearchItems.builder()
                .item(new SearchItem("taskCode", pono, null, SearchItem.Operator.EQ))
                .item(new SearchItem("deleted", false, null, SearchItem.Operator.EQ)).build());
        ProductionTask task = surveillanceService.findOne(condition);
        if (Objects.isNull(task)) {
            throw new RuntimeException(String.format("任务不存在；任务号：%s", pono));
        }
        if (Objects.isNull(craftParams) || craftParams.isEmpty()) {
            throw new RuntimeException("缺少工艺参数");
        }
        //查询工步以及执行工步
        ProcessStep step = task.getStep();
        WorkStep workStep = workStepRepository.findFirstByStepIdAndTaskTaskIdAndDeleted(step.getId(), task.getTaskId(), false);
        Map<String, StepParameter> stepParameterMap = findStepParameterMap(step);

        Map<String, ParameterDefinition> parameterDefMap = findParameterDefMap(workStep);
        List<StepParameter> stepParameters = new ArrayList<>();

        List<ParameterDefinition> parameterDefinitions = new ArrayList<>();
        Map<String, ParameterDefinitionExtend> parameterDefinitionExtendMap = new HashMap<>();
        final int[] order = {1};
        //查询执行工序
        craftParams.iterator().forEachRemaining(craftParam -> {
            stepParameters.add(buildStepParameter(stepParameterMap, craftParam,step, order[0]));
            ParameterDefinition parameterDefinition = buildParameter(parameterDefMap, craftParam, workStep);
            parameterDefinitions.add(parameterDefinition);
            if (Objects.nonNull(craftParam.get("extendAttr"))) {
                ParameterDefinitionExtend parameterDefinitionExtend = new ParameterDefinitionExtend();
                parameterDefinitionExtend.setExtendAttr(JsonUtils.toMap(craftParam.get("extendAttr")));
                parameterDefinitionExtendMap.put(parameterDefinition.getParamName(), parameterDefinitionExtend);
            }
            order[0]++;
        });
        stepParameterRepository.saveAll(stepParameters);
        List<ParameterDefinition> paramDefs = parameterDefinitionRepository.saveAll(parameterDefinitions);

        List<ParameterDefinitionExtend> extendList = paramDefs.stream().map(paramDef -> {
            String paramName = paramDef.getParamName();
            ParameterDefinitionExtend parameterDefinitionExtend = parameterDefinitionExtendMap.get(paramName);
            if (Objects.nonNull(parameterDefinitionExtend)) {
                parameterDefinitionExtend.setParamDefId(paramDef.getParamDefId());
            }
            return parameterDefinitionExtend;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(extendList)){
            parameterDefinitionExtendRepository.saveAll(extendList);
        }
    }

    /**
     * 查询工步下的工步参数，按照名称映射
     * @param step
     * @return
     */
    private Map<String,StepParameter> findStepParameterMap(ProcessStep step){
        //查询工步参数
        List<StepParameter> stepParameters = stepParameterRepository.findByStepIdOrderByCreatedTime(step.getId());
        Map<String, StepParameter> stepParameterMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(stepParameters)){
            stepParameterMap = Maps.uniqueIndex(stepParameters, StepParameter::getParamName);
        }
        return stepParameterMap;
    }

    /**
     * 查询执行工步下的工步参数，按照名称映射
     * @param workStep
     * @return
     */
    private Map<String,ParameterDefinition> findParameterDefMap(WorkStep workStep){
        //查询工步参数
        List<ParameterDefinition> parameterDefinitions = parameterDefinitionRepository.findByWorkStepWorkStepId(workStep.getWorkStepId());
        Map<String, ParameterDefinition> parameterDefinitionMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(parameterDefinitions)){
            parameterDefinitionMap = Maps.uniqueIndex(parameterDefinitions, ParameterDefinition::getParamName);
        }
        return parameterDefinitionMap;
    }

    /**
     * 构建保存更新的工艺参数
     * @param stepParameterMap
     * @param craftParam
     * @param step
     * @param order
     * @return
     */
    private StepParameter buildStepParameter(Map<String,StepParameter> stepParameterMap, JsonNode craftParam,ProcessStep step,int order) {
        StepParameter stepParameter = stepParameterMap.getOrDefault(craftParam.get("paramName").asText(), new StepParameter());
        stepParameter.setStep(step);
        stepParameter.setParamName(craftParam.get("paramName").asText());
        stepParameter.setParamOrder(order);
        return stepParameter;
    }

    /**
     * 构建保存更新的执行工艺参数
     * @param parameterDefMap
     * @param craftParam
     * @param workStep
     * @return
     */
    private ParameterDefinition buildParameter(Map<String,ParameterDefinition> parameterDefMap, JsonNode craftParam,WorkStep workStep) {
        ParameterDefinition parameterDefinition = parameterDefMap.getOrDefault(craftParam.get("paramName").asText(), new ParameterDefinition());
        parameterDefinition.setParamName(craftParam.get("paramName").asText());
        if (Objects.nonNull(craftParam.get("maxValue"))) {
            parameterDefinition.setMaxValue(new BigDecimal(craftParam.get("maxValue").asText()));
        }
        if (Objects.nonNull(craftParam.get("minValue"))) {
            parameterDefinition.setMinValue(new BigDecimal(craftParam.get("minValue").asText()));
        }
        if (Objects.nonNull(craftParam.get("targetValue"))) {
            parameterDefinition.setTargetValue(craftParam.get("targetValue").asText());
        }
        parameterDefinition.setWorkStep(workStep);
        return parameterDefinition;
    }

    public void paramAck(String json) {
        JsonNode jsonNode = JsonUtils.toJsonNode(json);
        String matNo = jsonNode.get("matNo").asText();
        String orderNo = jsonNode.get("orderNo").asText();
        String flag = jsonNode.get("replyFlag").asText();
        String replyDesc = jsonNode.get("replyDesc").asText();
        if (StringUtils.isBlank(matNo) || StringUtils.isBlank(orderNo) || StringUtils.isBlank(flag)) {
            throw new RuntimeException(String.format("G1MTTB:缺少参数；入口材料号：%s,合同号：%s,应答标记：%s", matNo, orderNo, flag));
        }
        if (!StringUtils.equals(flag, "0") && !StringUtils.equals(flag, "1")) {
            throw new RuntimeException(String.format("G1MTTB:参数错误；应答标识：%s", flag));
        }
        ParamConfirmed confirmed = StringUtils.equals(flag, "0") ? ParamConfirmed.received : ParamConfirmed.abnormal;
        //从缓存中获取工艺参数
        JsonNode paramNode = cacheManager.getAndRemovePendingParam(matNo, orderNo);
        if (Objects.isNull(paramNode)) {
            //确认电文先放入缓存
            cacheManager.addConfirmedParamsCache(matNo, orderNo, confirmed);
            return;
        }
        //如果存在确认电文，则将当前电文发送到kafka
        switch (confirmed) {
            case received ->  generateParams(paramNode.get("planNo").asText(), (ArrayNode) paramNode.get("craftParams"));
            case abnormal -> log.info("工艺参数: 应答异常，异常原因：{}", replyDesc);

        }
    }
}
