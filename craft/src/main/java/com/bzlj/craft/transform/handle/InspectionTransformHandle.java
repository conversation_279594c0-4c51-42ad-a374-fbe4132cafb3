package com.bzlj.craft.transform.handle;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import com.bzlj.craft.entity.ProductionTask;
import com.bzlj.craft.service.ISurveillanceService;
import com.bzlj.craft.transform.common.CommonHandler;
import com.bzlj.craft.transform.repository.TelegramRepository;
import com.bzlj.craft.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 检化验数据转换处理器
 * <p>
 * 负责处理检化验数据消息的转换和处理，主要功能包括：
 * 1. 接收检化验数据消息
 * 2. 解析任务编码并查询对应的生产任务
 * 3. 处理检化验数据的转换逻辑（待实现）
 * 4. 清理相关的电报数据
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@MessageHandler(messageType = "check_data", desc = "检化验数据转化")
public class InspectionTransformHandle extends CommonHandler<String> {

    /**
     * 电报仓储，用于管理电报数据
     */
    @Autowired
    private TelegramRepository telegramRepository;

    /**
     * 监控服务，用于查询和管理生产任务
     */
    @Autowired
    private ISurveillanceService surveillanceService;

    /**
     * 转换处理检化验数据消息
     * <p>
     * 接收检化验数据的JSON消息，解析任务编码并查询对应的生产任务
     * 注意：当前实现仅包含任务查询逻辑，具体的检化验数据处理逻辑待实现
     * </p>
     *
     * @param json 检化验数据的JSON字符串
     */
    @Transactional(rollbackFor = Exception.class)
    public void transform(String json, Map<String, Object> handleContext) {
        JsonNode jsonNode = JsonUtils.toJsonNode(json);
        // 查询任务
        String taskCode = jsonNode.get("taskCode").asText();
        ProductionTask task = surveillanceService.findOneByTaskCode(taskCode);

        // TODO: 实现具体的检化验数据处理逻辑
    }

    /**
     * 处理历史遗留数据
     * <p>
     * 当前实现为空，暂无需处理历史遗留数据
     * </p>
     *
     * @param relationIds 关联ID列表
     */
    @Override
    public void dealLegacyData(List<String> relationIds, Map<String, Object> handleContext) {
        // 暂无需处理历史遗留数据
    }

    /**
     * 获取关联ID列表
     * <p>
     * 当前实现返回空列表，暂无关联ID需要处理
     * </p>
     *
     * @param s 消息载荷
     * @return 空的关联ID列表
     */
    @Override
    public List<String> getRelationIds(String s) {
        return List.of();
    }

    /**
     * 清理关联数据
     * <p>
     * 根据电报ID删除相关的电报数据
     * </p>
     *
     * @param telegramId 电报ID，如果为空则不执行删除操作
     */
    @Override
    public void clearRelationData(String telegramId) {
        if(StringUtils.isEmpty(telegramId)) return;
        telegramRepository.deleteById(telegramId);
    }
}
