package com.bzlj.craft.transform.handle.pipe;

import bici.bzlj.dataprocess.core.annotation.MessageHandler;
import com.bzlj.craft.transform.common.CommonHandler;
import com.bzlj.craft.transform.repository.TelegramRepository;
import com.bzlj.craft.transform.service.CraftParamTransformService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 工艺参数转换处理器
 * <p>
 * 负责处理工艺参数消息的转换和处理，主要功能包括：
 * 1. 接收工艺参数消息
 * 2. 调用工艺参数转换服务进行参数处理
 * 3. 管理工艺参数的生命周期
 * 4. 清理相关的电报数据
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@MessageHandler(messageType = "pipe_craft_params", desc = "工艺参数转化")
public class PipeCraftParamTransformHandle extends CommonHandler<String> {

    /**
     * 工艺参数转换服务，用于处理工艺参数相关业务逻辑
     */
    @Autowired
    private CraftParamTransformService craftParamsTransform;

    /**
     * 电报仓储，用于管理电报数据
     */
    @Autowired
    private TelegramRepository telegramRepository;

    /**
     * 转换处理工艺参数消息
     * <p>
     * 接收工艺参数的JSON消息，调用工艺参数转换服务进行参数转换处理
     * </p>
     *
     * @param json 工艺参数的JSON字符串
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transform(String json, Map<String, Object> handleContext) {
        craftParamsTransform.craftParamsTransform(json);
    }

    /**
     * 清理关联数据
     * <p>
     * 根据电报ID删除相关的电报数据
     * </p>
     *
     * @param telegramId 电报ID，如果为空则不执行删除操作
     */
    @Override
    public void clearRelationData(String telegramId) {
        if(StringUtils.isEmpty(telegramId)) return;
        telegramRepository.deleteById(telegramId);
    }

    /**
     * 处理历史遗留数据
     * <p>
     * 当前实现为空，暂无需处理历史遗留数据
     * </p>
     *
     * @param relationIds 关联ID列表
     */
    @Override
    public void dealLegacyData(List<String> relationIds) {
        // 暂无需处理历史遗留数据
    }

    /**
     * 获取关联ID列表
     * <p>
     * 当前实现返回空列表，暂无关联ID需要处理
     * </p>
     *
     * @param s 消息载荷
     * @return 空的关联ID列表
     */
    @Override
    public List<String> getRelationIds(String s) {
        return List.of();
    }
}
